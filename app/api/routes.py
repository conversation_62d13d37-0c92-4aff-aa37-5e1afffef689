from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles

from app.core.logging import logger
from app.models.chat import ChatModel
from app.models.error import ErrorResponse
from app.models.embedding import EmbeddingRequest
from app.models.file import FileShareRequest
from app.models.image import ImageRequest, ImageSimilarity
from app.core.config import SHARED_FILES_DIR, ENDPOINT_CHECK_NSFW_IMAGE, ENDPOINT_VOICE
from app.models.config import ModelConfig, UpdateChatModelsRequest, UpdateModelRequest, VoiceRequest
from app.services.ollama import OllamaService
from app.services.image import ImageGenerationService, ImageGenerationManager
from app.services.embedding import EmbeddingService, EmbeddingManager
from app.services.config import ConfigService
from app.services.file import FileService
from app.utils.stable_diffusion import process_images_from_url
from app.utils.image_torch import ImageUtils
from app.utils.utils import fetch_url
import base64
import io
import httpx
import aiohttp
import asyncio
import json
from PIL import Image  # Cần cài đặt thư viện Pillow: pip install Pillow
import uuid
import os
import random
from datetime import datetime

# Create API router
router = APIRouter()

# Initialize services
ollama_service = None
image_service = None
embedding_service = None
image_manager = None
embedding_manager = None
config_service = ConfigService()

OPENROUTER_KEY = os.environ.get("OPEN_ROUTER_KEY", "")
OPENROUTER_MODEL = os.environ.get("DEFAULT_MODEL_OPENROUTER", "deepseek/deepseek-chat-v3-0324")

def initialize_services(model_managers):
    """Initialize the services with model managers"""
    global ollama_service, image_service, embedding_service, image_manager, embedding_manager

    # Initialize managers
    image_manager = ImageGenerationManager()
    embedding_manager = EmbeddingManager()
    image_utils = ImageUtils()
    # Initialize services
    ollama_service = OllamaService(model_managers)
    image_service = ImageGenerationService(image_manager, image_utils)
    embedding_service = EmbeddingService(embedding_manager)


async def call_openrouter(model_id_, message_content):
    api_key = os.environ.get("OPEN_ROUTER_KEY", "")
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://foxychat.ai",
        "X-Title": "Foxy Chat",
    }
    if "r1" in model_id_:
        payload = {
            "model": model_id_,
            "messages": message_content,
            "provider": {
                "sort": "latency"
            },
        }
    else:
        payload = {
            "model": model_id_,
            "messages": message_content,
            "temperature": 1.0,
            "provider": {
                "quantizations": [
                    "fp8"
                ],
                "sort": "latency"
            },
        }

    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(
            url=os.environ.get("ENDPOINT", "https://openrouter.ai/api/v1/chat/completions"),
            headers=headers,
            json=payload
        )
        print(response.status_code)
        return response.json()


@router.post("/chat")
async def chat(item: ChatModel):
    """
    Chat endpoint that routes requests to the least loaded model
    """
    try:
        if ollama_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")
        try:
            message_content = [
                {
                    "role": "system",
                    "content": item.messages[0]["content"]
                },
                {
                    "role": "user",
                    "content": item.messages[1]["content"]
                }
            ]
            completion = await call_openrouter(
                os.environ.get("DEFAULT_MODEL_OPENROUTER", "deepseek/deepseek-chat-v3-0324"), message_content)
            if '"message":' in completion["choices"][0]["message"]["content"]:
                completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                    '"message":', '"content":')
            if '"response":' in completion["choices"][0]["message"]["content"]:
                completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                    '"response":', '"content":')
            if completion["choices"][0]["message"]["content"].startswith("<think>"):
                response = {
                    "message": {
                        "content": completion["choices"][0]["message"]["content"].split("</think>")[-1],
                        "think": completion["choices"][0]["message"]["content"].split("</think>")[0].replace("<think>",
                                                                                                             "")
                    }
                }
            else:
                response = {
                    "message": {
                        "content": completion["choices"][0]["message"]["content"],
                        "think": completion["choices"][0]["message"]["content"]
                    }
                }
            logger.info(response)
            return JSONResponse(content=response, status_code=200)
        except Exception as e:
            logger.error(f"Unexpected error in chat endpoint: {str(e)}")
            response = {"msg": str(e)}
            return JSONResponse(content=response, status_code=500)
    except RuntimeError as e:
        logger.error(f"Runtime error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )

@router.post("/chat-stream")
async def chat_stream(item: ChatModel):
    """
    Streaming chat endpoint using OpenRouter API
    """
    url = os.environ.get("ENDPOINT", "https://openrouter.ai/api/v1/chat/completions")
    headers = {
        "Authorization": f"Bearer {OPENROUTER_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://foxychat.ai",
        "X-Title": "Foxy Chat",
    }

    message_content = [
        {
            "role": "system",
            "content": item.messages[0]["content"]
        },
        {
            "role": "user",
            "content": item.messages[1]["content"]
        }
    ]

    # Check if model is r1 type to adjust payload
    if "r1" in OPENROUTER_MODEL:
        payload = {
            "model": OPENROUTER_MODEL,
            "messages": message_content,
            "provider": {
                "sort": "latency"
            },
            "stream": True
        }
    else:
        payload = {
            "model": OPENROUTER_MODEL,
            "messages": message_content,
            "temperature": 1.0,
            "provider": {
                "quantizations": ["fp8"],
                "sort": "latency"
            },
            "stream": True
        }

    async def generate():
        try:
            async with httpx.AsyncClient(
                timeout=60.0,
                verify=False,
                follow_redirects=True
            ) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix

                            if data == '[DONE]':
                                yield f"{json.dumps({'done': True})}\n\n"
                                return

                            if data.strip():  # Skip empty data
                                try:
                                    data_obj = json.loads(data)
                                    if "choices" in data_obj and len(data_obj["choices"]) > 0:
                                        delta = data_obj["choices"][0].get("delta", {})
                                        content = delta.get("content")

                                        if content:
                                            chunk_response = {
                                                "message": {
                                                    "role": "assistant",
                                                    "content": content
                                                },
                                                "done": False
                                            }
                                            yield f"{json.dumps(chunk_response)}\n\n"

                                except json.JSONDecodeError:
                                    # Skip malformed JSON
                                    continue

        except httpx.HTTPError as e:
            logger.error(f"HTTP error in chat_stream: {str(e)}")
            yield f"data: {json.dumps({'error': f'HTTP error: {str(e)}'})}\n\n"
        except Exception as e:
            logger.error(f"Unexpected error in chat_stream: {str(e)}")
            yield f"data: {json.dumps({'error': f'Unexpected error: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.post("/chat-stream/{model_id}")
async def chat_stream_model(model_id: str, item: ChatModel):
    """
    Streaming chat endpoint with specific model using OpenRouter API
    """
    url = os.environ.get("ENDPOINT", "https://openrouter.ai/api/v1/chat/completions")
    headers = {
        "Authorization": f"Bearer {OPENROUTER_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://foxychat.ai",
        "X-Title": "Foxy Chat",
    }

    # Map model_id to actual OpenRouter model
    model_id_ = "deepseek/deepseek-chat-v3-0324"
    if model_id.endswith("qwen3-235b-a22b-07-25"):
        model_id_ = "qwen/qwen3-235b-a22b-07-25"
    elif model_id.endswith("gemini-2.5-flash-lite"):
        model_id_ = "google/gemini-2.5-flash-lite"
    elif model_id.endswith("grok-3-mini-beta"):
        model_id_ = "x-ai/grok-3-mini-beta"
    elif model_id.endswith("deepseek-r1-0528"):
        model_id_ = "deepseek/deepseek-r1-0528"
    elif model_id.endswith("deepseek-chat-v3-0324"):
        model_id_ = "deepseek/deepseek-chat-v3-0324"

    message_content = [
        {
            "role": "system",
            "content": item.messages[0]["content"]
        },
        {
            "role": "user",
            "content": item.messages[1]["content"]
        }
    ]

    # Check if model is r1 type to adjust payload
    if "r1" in model_id_:
        payload = {
            "model": model_id_,
            "messages": message_content,
            "provider": {
                "sort": "latency"
            },
            "stream": True
        }
    else:
        payload = {
            "model": model_id_,
            "messages": message_content,
            "temperature": 1.0,
            "provider": {
                "quantizations": ["fp8"],
                "sort": "latency"
            },
            "stream": True
        }

    async def generate():
        try:
            async with httpx.AsyncClient(
                timeout=60.0,
                verify=False,
                follow_redirects=True
            ) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            data = line[6:]  # Remove 'data: ' prefix

                            if data == '[DONE]':
                                yield f"data: {json.dumps({'done': True})}\n\n"
                                return

                            if data.strip():  # Skip empty data
                                try:
                                    data_obj = json.loads(data)
                                    if "choices" in data_obj and len(data_obj["choices"]) > 0:
                                        delta = data_obj["choices"][0].get("delta", {})
                                        content = delta.get("content")

                                        if content:
                                            chunk_response = {
                                                "message": {
                                                    "role": "assistant",
                                                    "content": content
                                                },
                                                "done": False
                                            }
                                            yield f"data: {json.dumps(chunk_response)}\n\n"

                                except json.JSONDecodeError:
                                    # Skip malformed JSON
                                    continue

        except httpx.HTTPError as e:
            logger.error(f"HTTP error in chat_stream_model: {str(e)}")
            yield f"data: {json.dumps({'error': f'HTTP error: {str(e)}'})}\n\n"
        except Exception as e:
            logger.error(f"Unexpected error in chat_stream_model: {str(e)}")
            yield f"data: {json.dumps({'error': f'Unexpected error: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@router.post("/chat-model/{model_id}")
async def chat(model_id: str, item: ChatModel):
    """
    Chat endpoint that routes requests to the least loaded model
    """
    try:
        if ollama_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")
        model_id_ = "deepseek/deepseek-chat-v3-0324"
        if model_id.endswith("qwen3-235b-a22b-07-25"):
            model_id_ = "qwen/qwen3-235b-a22b-07-25"
        elif model_id.endswith("gemini-2.5-flash-lite"):
            model_id_ = "google/gemini-2.5-flash-lite"
        elif model_id.endswith("grok-3-mini-beta"):
            model_id_ = "x-ai/grok-3-mini-beta"
        elif model_id.endswith("deepseek-r1-0528"):
            model_id_ = "deepseek/deepseek-r1-0528"
        elif model_id.endswith("deepseek-chat-v3-0324"):
            model_id_ = "deepseek/deepseek-chat-v3-0324"
        message_content = [
            {
                "role": "system",
                "content": item.messages[0]["content"]
            },
            {
                "role": "user",
                "content": item.messages[1]["content"]
            }
        ]
        completion = await call_openrouter(model_id_, message_content)
        if '"message":' in completion["choices"][0]["message"]["content"]:
            completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                '"message":', '"content":')
        if '"response":' in completion["choices"][0]["message"]["content"]:
            completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                '"response":', '"content":')
        if completion["choices"][0]["message"]["content"].startswith("<think>"):
            response = {
                "message": {
                    "content": completion["choices"][0]["message"]["content"].split("</think>")[-1],
                    "think": completion["choices"][0]["message"]["content"].split("</think>")[0].replace("<think>", "")
                }
            }
        else:
            response = {
                "message": {
                    "content": completion["choices"][0]["message"]["content"],
                    "think": completion["choices"][0]["message"]["content"]
                }
            }
        logger.info(response)
        return JSONResponse(content=response, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/generate-image")
async def generate_image(item: ChatModel):
    """
    Generate image endpoint that uses a specific model for image generation
    """
    try:
        if image_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        response = await image_service.generate_image(item.messages)
        if "<think>" in response["message"]["content"]:
            think_content = response["message"]["content"].split("</think>")[0]
            response["message"]["content"] = response["message"]["content"].split("</think>")[-1]
            response["message"]["think"] = think_content.replace("<think>", "")
        return JSONResponse(content=response, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in generate-image endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/embeddings")
async def generate_embeddings(item: EmbeddingRequest):
    """
    Generate embeddings for the given messages
    """
    try:
        if embedding_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        response = await embedding_service.generate_embedding(item.messages, item.model)
        return JSONResponse(content=response, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in embeddings endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in embeddings endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/usage")
async def get_model_usage():
    """
    Get current usage statistics for all models
    """
    try:
        if ollama_service is None or image_service is None or embedding_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        # Get chat model usage
        chat_usage = ollama_service.get_model_usage()

        # Get image model usage
        image_usage = image_service.get_usage()

        # Get embedding model usage
        embedding_usage = embedding_service.get_usage()

        return JSONResponse(
            content={
                "chat": chat_usage,
                "image": image_usage,
                "embedding": embedding_usage
            },
            status_code=200
        )
    except Exception as e:
        logger.error(f"Unexpected error in usage endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/models/config")
async def get_model_config():
    """
    Get the current model configuration
    """
    try:
        config = config_service.get_model_config()
        return JSONResponse(content=config, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in get_model_config endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/chat")
async def update_chat_models(request: UpdateChatModelsRequest):
    """
    Update the chat models list
    """
    try:
        result = config_service.update_chat_models(request.operation, request.models)

        # If we have an active ollama service, we need to reinitialize it
        if ollama_service is not None:
            # Get the updated model list
            from app.core.config import MODEL_LIST
            # Create new model managers
            from app.services.ollama import OllamaManager
            model_managers = [OllamaManager(model) for model in MODEL_LIST]
            # Reinitialize the service
            initialize_services(model_managers)

        return JSONResponse(content=result, status_code=200)
    except ValueError as e:
        logger.error(f"Value error in update_chat_models endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in update_chat_models endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/image")
async def update_image_model(request: UpdateModelRequest):
    """
    Update the image generation model
    """
    try:
        result = config_service.update_image_model(request.model)

        # If we have an active image service, we need to update its model
        if image_manager is not None:
            image_manager.model = request.model

        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in update_image_model endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.put("/models/embedding")
async def update_embedding_model(request: UpdateModelRequest):
    """
    Update the text embedding model
    """
    try:
        result = config_service.update_embedding_model(request.model)

        # If we have an active embedding service, we need to update its model
        if embedding_manager is not None:
            embedding_manager.model = request.model

        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in update_embedding_model endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/health-test")
async def health_check_test():
    """Health check endpoint for testing - includes delay"""
    import asyncio
    import uuid
    print("start: ", uuid.uuid4())
    await asyncio.sleep(10)
    print("Done: ", uuid.uuid4())
    return {"status": "healthy"}


@router.post("/generate-voice")
async def generate_voice(request: VoiceRequest):
    try:
        # Prepare the payload for the external voice API
        payload = {
            "text": request.content.replace("\n", " "),
            "voice_id": request.voice_id or "af_heart",
            "prompt_text": request.prompt_text,
            "instruct_text": request.instruct_text,
            "format": "wav",
            "speed": 1,
            "stream": False
        }

        # Construct the full API endpoint URL
        voice_api_url = f"{ENDPOINT_VOICE}/api/v1/cross-lingual/with-cache"
        logger.info(f"Calling voice API at {voice_api_url} with payload: {payload}")

        # Call the external voice API
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(voice_api_url, json=payload)

            if response.status_code == 404:
                logger.error(f"Voice API endpoint not found: {voice_api_url}")
                return JSONResponse(
                    content=ErrorResponse(error=f"Voice API endpoint not available at {voice_api_url}").dict(),
                    status_code=503
                )
            elif response.status_code != 200:
                logger.error(f"Voice API returned status {response.status_code}: {response.text}")
                return JSONResponse(
                    content=ErrorResponse(error=f"Voice API returned error: {response.status_code} - {response.text}").dict(),
                    status_code=500
                )

            # Parse the response from the external API
            result = response.json()
            logger.info(f"Voice API response: {result}")

            # Check if the API call was successful
            if not result.get("success", False):
                error_message = result.get('message', 'Unknown error')
                logger.error(f"Voice API returned success=false: {error_message}")

                # Provide helpful error messages for common issues
                if 'not found' in error_message.lower():
                    return JSONResponse(
                        content=ErrorResponse(error=f"Voice ID '{request.voice_id}' not found in cache. Please create or upload a voice first using the voice management endpoints.").dict(),
                        status_code=404
                    )
                else:
                    return JSONResponse(
                        content=ErrorResponse(error=f"Voice generation failed: {error_message}").dict(),
                        status_code=500
                    )

            # Transform the response to match our expected format
            # If audio_url is provided, use it directly; otherwise construct the share URL
            if result.get("audio_url"):
                file_url = result["audio_url"]
            elif result.get("file_path"):
                # Extract filename from file_path and construct share URL
                filename = result["file_path"].split("/")[-1] if "/" in result["file_path"] else result["file_path"]
                file_url = f"{ENDPOINT_VOICE}/api/v1/share-file-voice/{filename}"
            else:
                logger.error("Voice API response missing audio_url and file_path")
                return JSONResponse(
                    content=ErrorResponse(error="Voice API response missing audio information").dict(),
                    status_code=500
                )

            # Return response in our expected format
            response_data = {
                "filename": result.get("file_path", "").split("/")[-1] if result.get("file_path") else "audio.wav",
                "url": file_url,
                "duration": result.get("duration", 0),
                "format": result.get("format", "wav"),
                "synthesis_time": result.get("synthesis_time", 0),
                "message": result.get("message", "Voice generated successfully")
            }

            return JSONResponse(content=response_data, status_code=200)

    except httpx.ConnectError as e:
        logger.error(f"Connection error to voice API: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=f"Cannot connect to voice API at {ENDPOINT_VOICE}").dict(),
            status_code=503
        )
    except httpx.HTTPError as e:
        # Check if it's a 404 error specifically
        if hasattr(e, 'response') and e.response.status_code == 404:
            logger.error(f"Voice API endpoint not found: {ENDPOINT_VOICE}")
            return JSONResponse(
                content=ErrorResponse(error=f"Voice API endpoint not available at {ENDPOINT_VOICE}").dict(),
                status_code=503
            )
        else:
            logger.error(f"HTTP error in generate_voice endpoint: {str(e)}")
            return JSONResponse(
                content=ErrorResponse(error=f"Voice API error: {str(e)}").dict(),
                status_code=500
            )
    except Exception as e:
        logger.error(f"Unexpected error in generate_voice endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.get("/voices")
async def list_voices():
    """
    Get list of available voices from the voice API
    """
    try:
        voice_api_url = f"{ENDPOINT_VOICE}/api/v1/voices/"
        logger.info(f"Fetching voices from {voice_api_url}")

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(voice_api_url)

            if response.status_code != 200:
                logger.error(f"Voice API returned status {response.status_code}: {response.text}")
                return JSONResponse(
                    content=ErrorResponse(error=f"Failed to fetch voices: {response.status_code}").dict(),
                    status_code=500
                )

            result = response.json()
            logger.info(f"Voice API response: {result}")

            # Also get pretrained voices
            pretrained_url = f"{ENDPOINT_VOICE}/api/v1/voices/pretrained/list"
            pretrained_response = await client.get(pretrained_url)
            pretrained_voices = []

            if pretrained_response.status_code == 200:
                pretrained_data = pretrained_response.json()
                pretrained_voices = pretrained_data.get("pretrained_voices", [])

            # Combine results
            response_data = {
                "cached_voices": result.get("voices", []),
                "pretrained_voices": pretrained_voices,
                "total_cached": result.get("total", 0),
                "total_pretrained": len(pretrained_voices),
                "message": "Use voice_id from cached_voices or pretrained_voices for voice generation"
            }

            return JSONResponse(content=response_data, status_code=200)

    except httpx.ConnectError as e:
        logger.error(f"Connection error to voice API: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=f"Cannot connect to voice API at {ENDPOINT_VOICE}").dict(),
            status_code=503
        )
    except Exception as e:
        logger.error(f"Unexpected error in list_voices endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/share-file")
async def share_file(request: FileShareRequest):
    """
    Share a file and get a URL to access it
    """
    try:
        # Use the file service to save the file
        file_service = FileService()
        result = file_service.save_shared_file(
            content=request.content,
            filename=request.filename,
            file_type=request.file_type
        )

        return JSONResponse(content=result, status_code=200)
    except RuntimeError as e:
        logger.error(f"Runtime error in share_file endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in share_file endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )


@router.post("/stable-diffusion")
async def generate_image_stable_diffusion(request: ImageRequest):
    """
    Generate image using Stable Diffusion API endpoint
    """
    logger.info("start stable-diffusion")
    url_endpoints = []
    for _ in range(3):
        if _ == 0:
            url_endpoints.append(os.environ.get("ENDPOINT_GEN_IMAGE", ""))
        else:
            url_endpoints.append(os.environ.get(f"ENDPOINT_GEN_IMAGE{_}", ""))
    try:
        if request.model_id.startswith("nova"):
            url_endpoint = random.choice(url_endpoints)
        else:
            url_endpoint = os.environ.get("ENDPOINT_GEN_IMAGE_REAL", "")
        logger.info(f"call api {url_endpoint}")

        if not url_endpoint:
            raise HTTPException(status_code=500, detail="ENDPOINT_GEN_IMAGE environment variable not set")
        request_body = request.model_dump()
        logger.info(request_body)
        data_task = await fetch_url(url_endpoint, request_body)
        return JSONResponse(content={"data": data_task}, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in stable-diffusion endpoint: {str(e)}")
        return JSONResponse(content={"msg": f"Error {e}"}, status_code=500)


@router.post("/stable-diffusion/{id_task}")
async def result_image_stable_diffusion(id_task: str, request: Request):
    """
        Generate image using Stable Diffusion API endpoint
        """
    try:
        data_json = await request.json()
        url_endpoint = f"{data_json['ip_process']}/sdapi/v1/result/{id_task}"
        result_task = await process_images_from_url(url_endpoint, {}, True, data_json["image_file"])
        from app.core.config import BASE_URL
        if isinstance(result_task, list):
            logger.info(str(result_task))
            return JSONResponse(content={
                "data": result_task,
                "url": f"{BASE_URL}/share/{result_task[0]}"
            }, status_code=200)
        else:
            return JSONResponse(content=result_task, status_code=200)

    except Exception as e:
        return JSONResponse(content={
            "msg": str(e)
        }, status_code=500)


@router.post("/clc-similarity")
async def clc_similarity(request: ImageSimilarity):
    try:
        if image_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")

        logger.info("Starting image similarity calculation")
        similarity = await image_service.clc_similarity_image(request.image_compare, request.image_red)
        logger.info(f"Image similarity calculation completed: {similarity}")
        return JSONResponse(content={"data": {"similarity": float(similarity)}}, status_code=200)
    except Exception as e:
        logger.error(f"Error in clc-similarity endpoint: {str(e)}")
        return JSONResponse(content={"error": f"error: {e}"}, status_code=500)


@router.get("/health")
async def health_check():
    """Health check endpoint to monitor application status"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": str(datetime.now()),
            "services": {
                "ollama_service": "available" if ollama_service is not None else "unavailable",
                "image_service": "available" if image_service is not None else "unavailable",
                "embedding_service": "available" if embedding_service is not None else "unavailable"
            }
        }

        # Check if any critical services are unavailable
        if any(status == "unavailable" for status in health_status["services"].values()):
            health_status["status"] = "degraded"

        return JSONResponse(content=health_status, status_code=200)
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        return JSONResponse(content={
            "status": "unhealthy",
            "error": str(e),
            "timestamp": str(datetime.now())
        }, status_code=500)


@router.get("/metrics")
async def get_metrics():
    """Get application metrics and usage statistics"""
    try:
        metrics = {
            "timestamp": str(datetime.now()),
            "ollama_models": ollama_service.get_model_usage() if ollama_service else {},
            "image_generation": image_service.get_usage() if image_service else {},
            "embedding_service": {
                "tpm": embedding_manager.tpm if embedding_manager else 0,
                "rpm": embedding_manager.rpm if embedding_manager else 0,
                "max_tpm": embedding_manager.max_tpm if embedding_manager else 0,
                "max_rpm": embedding_manager.max_rpm if embedding_manager else 0
            }
        }
        return JSONResponse(content=metrics, status_code=200)
    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        return JSONResponse(content={"error": str(e)}, status_code=500)


@router.get("/check_nsfw_style_image")
async def check_image(image_url: str):
    try:
        url_endpoint = ENDPOINT_CHECK_NSFW_IMAGE
        data_task = await fetch_url(url_endpoint, {"image_url": image_url})
        if data_task is None:
            data_task = {"status": "error", "message": "Image URL is not valid", "is_nsfw": False}
        return JSONResponse(content=data_task, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error in check_nsfw_style_image endpoint: {str(e)}")
        return JSONResponse(content={"error": f"Error {e}"}, status_code=500)


@router.post("/extract-keywords")
async def extract_keywords(item: ChatModel):
    """
    Chat endpoint that routes requests to the least loaded model
    """
    try:
        if ollama_service is None:
            raise HTTPException(status_code=500, detail="Service not initialized")
        try:
            message_content = [
                {
                    "role": "system",
                    "content": item.messages[0]["content"]
                },
                {
                    "role": "user",
                    "content": item.messages[1]["content"]
                }
            ]
            completion = await call_openrouter(
                os.environ.get("MODEL_EXTRACT_KEYWORDS", "deepseek/deepseek-chat-v3-0324"), message_content)
            if '"message":' in completion["choices"][0]["message"]["content"]:
                completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                    '"message":', '"content":')
            if '"response":' in completion["choices"][0]["message"]["content"]:
                completion["choices"][0]["message"]["content"] = completion["choices"][0]["message"]["content"].replace(
                    '"response":', '"content":')
            if completion["choices"][0]["message"]["content"].startswith("<think>"):
                response = {
                    "message": {
                        "content": completion["choices"][0]["message"]["content"].split("</think>")[-1],
                        "think": completion["choices"][0]["message"]["content"].split("</think>")[0].replace("<think>",
                                                                                                             "")
                    }
                }
            else:
                response = {
                    "message": {
                        "content": completion["choices"][0]["message"]["content"],
                        "think": completion["choices"][0]["message"]["content"]
                    }
                }
            logger.info(response)
            return JSONResponse(content=response, status_code=200)
        except Exception as e:
            logger.error(f"Unexpected error in chat endpoint: {str(e)}")
            response = {"msg": str(e)}
            return JSONResponse(content=response, status_code=500)
    except RuntimeError as e:
        logger.error(f"Runtime error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error=str(e)).dict(),
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        return JSONResponse(
            content=ErrorResponse(error="An unexpected error occurred").dict(),
            status_code=500
        )
