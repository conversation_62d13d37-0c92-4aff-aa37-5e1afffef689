from typing import List
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API Configuration
URL_ENDPOINT = []
for _ in range(4):
    if _ == 0:
        URL_ENDPOINT.append(os.getenv("OLLAMA_API_ENDPOINT", "http://34.143.181.21:11434/api/chat"))
    else:
        EP_OS = f"OLLAMA_API_ENDPOINT{_}"
        URL_ENDPOINT.append(os.getenv(EP_OS, "http://34.143.181.21:11434/api/chat"))
EMBEDDING_ENDPOINT = os.getenv("OLLAMA_EMBEDDING_URL", "http://34.143.181.21:11434/api/embeddings")
ENDPOINT_VOICE = os.getenv("ENDPOINT_VOICE", "http://**************:19596")

# Model Configuration
MODEL_LIST = [
    "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M",
    "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M"
]

MODEL_GEN_IMAGE = os.getenv("IMAGE_MODEL", "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M")
MODEL_EMBEDDING = os.getenv("EMBEDDING_MODEL", "nomic-embed-text")

# Server Configuration
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8010"))
BASE_URL = os.getenv("BASE_URL", f"http://{HOST}:{PORT}")

# File Sharing Configuration
SHARED_FILES_DIR = os.getenv("SHARED_FILES_DIR", "shared_files")

# Rate Limiting Configuration
MAX_TPM = int(os.getenv("MAX_TPM", "500000"))  # Maximum tokens per minute
MAX_RPM = int(os.getenv("MAX_RPM", "600"))  # Maximum requests per minute

ENDPOINT_CHECK_NSFW_IMAGE = "http://**************:5002/check_single_image"
